from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
import os
from datetime import datetime

class Database:
    def __init__(self):
        self.client = None
        self.db = None
        
    def connect(self, connection_string, database_name="bookmark_manager"):
        """连接到MongoDB数据库"""
        try:
            self.client = MongoClient(connection_string)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[database_name]
            print(f"成功连接到MongoDB数据库: {database_name}")
            return True
        except ConnectionFailure as e:
            print(f"MongoDB连接失败: {e}")
            return False
        except Exception as e:
            print(f"数据库连接错误: {e}")
            return False
    
    def get_collection(self, collection_name):
        """获取集合"""
        if self.db is None:
            raise Exception("数据库未连接")
        return self.db[collection_name]
    
    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()

# 全局数据库实例
db_instance = Database()

def init_database(app):
    """初始化数据库连接"""
    connection_string = "*******************************************/"
    success = db_instance.connect(connection_string)
    
    if success:
        # 创建索引以提高查询性能
        bookmarks_collection = db_instance.get_collection('bookmarks')
        
        # 为常用查询字段创建索引
        bookmarks_collection.create_index("title")
        bookmarks_collection.create_index("tags")
        bookmarks_collection.create_index("urgency")
        bookmarks_collection.create_index("importance")
        bookmarks_collection.create_index("created_at")
        
        print("数据库索引创建完成")
    
    return success

