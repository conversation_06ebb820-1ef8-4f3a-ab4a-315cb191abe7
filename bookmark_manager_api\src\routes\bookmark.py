from flask import Blueprint, request, jsonify
from src.models.bookmark import bookmark_model
from datetime import datetime

bookmark_bp = Blueprint('bookmark', __name__)

@bookmark_bp.route('/bookmarks', methods=['GET'])
def get_bookmarks():
    """获取书签列表"""
    try:
        # 获取查询参数
        filters = {}
        if request.args.get('tags'):
            filters['tags'] = request.args.get('tags')
        if request.args.get('urgency'):
            filters['urgency'] = request.args.get('urgency')
        if request.args.get('importance'):
            filters['importance'] = request.args.get('importance')
        if request.args.get('search'):
            filters['search'] = request.args.get('search')
        if request.args.get('reminder_start'):
            filters['reminder_start'] = request.args.get('reminder_start')
        if request.args.get('reminder_end'):
            filters['reminder_end'] = request.args.get('reminder_end')
        
        # 分页参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        
        # 排序参数
        sort_by = request.args.get('sort', 'created_at')
        sort_order = -1 if request.args.get('order', 'desc') == 'desc' else 1
        
        # 获取书签
        result = bookmark_model.get_bookmarks(filters, page, limit, sort_by, sort_order)
        
        return jsonify({
            'success': True,
            'data': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'GET_BOOKMARKS_ERROR',
                'message': f'获取书签列表失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks', methods=['POST'])
def create_bookmark():
    """创建新书签"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_DATA',
                    'message': '请求数据不能为空'
                }
            }), 400
        
        # 创建书签
        bookmark = bookmark_model.create_bookmark(data)
        
        return jsonify({
            'success': True,
            'data': bookmark
        }), 201
    
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': str(e)
            }
        }), 400
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'CREATE_BOOKMARK_ERROR',
                'message': f'创建书签失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks/<bookmark_id>', methods=['GET'])
def get_bookmark(bookmark_id):
    """获取单个书签"""
    try:
        bookmark = bookmark_model.get_bookmark_by_id(bookmark_id)
        
        if not bookmark:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'BOOKMARK_NOT_FOUND',
                    'message': '书签不存在'
                }
            }), 404
        
        return jsonify({
            'success': True,
            'data': bookmark
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'GET_BOOKMARK_ERROR',
                'message': f'获取书签失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks/<bookmark_id>', methods=['PUT'])
def update_bookmark(bookmark_id):
    """更新书签"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_DATA',
                    'message': '请求数据不能为空'
                }
            }), 400
        
        # 更新书签
        bookmark = bookmark_model.update_bookmark(bookmark_id, data)
        
        if not bookmark:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'BOOKMARK_NOT_FOUND',
                    'message': '书签不存在或更新失败'
                }
            }), 404
        
        return jsonify({
            'success': True,
            'data': bookmark
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'UPDATE_BOOKMARK_ERROR',
                'message': f'更新书签失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks/<bookmark_id>', methods=['DELETE'])
def delete_bookmark(bookmark_id):
    """删除书签"""
    try:
        success = bookmark_model.delete_bookmark(bookmark_id)
        
        if not success:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'BOOKMARK_NOT_FOUND',
                    'message': '书签不存在或删除失败'
                }
            }), 404
        
        return jsonify({
            'success': True,
            'message': '书签删除成功'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'DELETE_BOOKMARK_ERROR',
                'message': f'删除书签失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks/batch', methods=['DELETE'])
def delete_bookmarks_batch():
    """批量删除书签"""
    try:
        data = request.get_json()
        
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_DATA',
                    'message': '请提供要删除的书签ID列表'
                }
            }), 400
        
        deleted_count = bookmark_model.delete_bookmarks_batch(data['ids'])
        
        return jsonify({
            'success': True,
            'data': {
                'deleted_count': deleted_count
            },
            'message': f'成功删除 {deleted_count} 个书签'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'BATCH_DELETE_ERROR',
                'message': f'批量删除失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/bookmarks/batch/tags', methods=['POST'])
def add_tags_batch():
    """批量添加标签"""
    try:
        data = request.get_json()
        
        if not data or 'bookmark_ids' not in data or 'tags' not in data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_DATA',
                    'message': '请提供书签ID列表和标签列表'
                }
            }), 400
        
        bookmark_ids = data['bookmark_ids']
        new_tags = data['tags']
        updated_count = 0
        
        for bookmark_id in bookmark_ids:
            bookmark = bookmark_model.get_bookmark_by_id(bookmark_id)
            if bookmark:
                # 合并标签，去重
                existing_tags = bookmark.get('tags', [])
                merged_tags = list(set(existing_tags + new_tags))
                
                # 更新书签
                result = bookmark_model.update_bookmark(bookmark_id, {'tags': merged_tags})
                if result:
                    updated_count += 1
        
        return jsonify({
            'success': True,
            'data': {
                'updated_count': updated_count
            },
            'message': f'成功为 {updated_count} 个书签添加标签'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'BATCH_ADD_TAGS_ERROR',
                'message': f'批量添加标签失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/tags', methods=['GET'])
def get_tags():
    """获取标签统计"""
    try:
        tags = bookmark_model.get_tag_stats()
        
        return jsonify({
            'success': True,
            'data': tags
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'GET_TAGS_ERROR',
                'message': f'获取标签统计失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        stats = bookmark_model.get_stats()
        
        return jsonify({
            'success': True,
            'data': stats
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'GET_STATS_ERROR',
                'message': f'获取统计信息失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/sync/export', methods=['GET'])
def export_data():
    """导出所有数据"""
    try:
        data = bookmark_model.export_all_data()
        
        return jsonify({
            'success': True,
            'data': {
                'bookmarks': data,
                'exported_at': datetime.utcnow().isoformat(),
                'count': len(data)
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'EXPORT_ERROR',
                'message': f'导出数据失败: {str(e)}'
            }
        }), 500

@bookmark_bp.route('/sync/import', methods=['POST'])
def import_data():
    """导入数据"""
    try:
        data = request.get_json()
        
        if not data or 'bookmarks' not in data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_DATA',
                    'message': '请提供要导入的书签数据'
                }
            }), 400
        
        imported_count = bookmark_model.import_data(data['bookmarks'])
        
        return jsonify({
            'success': True,
            'data': {
                'imported_count': imported_count
            },
            'message': f'成功导入 {imported_count} 个书签'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'IMPORT_ERROR',
                'message': f'导入数据失败: {str(e)}'
            }
        }), 500

