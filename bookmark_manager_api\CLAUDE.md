# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A Flask-based REST API for a bookmark manager with MongoDB backend, providing CRUD operations, tagging, filtering, and data import/export capabilities for a Vue2 frontend.

## Development Commands

### Setup & Installation
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### Running the Application
```bash
# Start the Flask development server
python src/main.py

# Server runs on http://0.0.0.0:5000
```

### Database Configuration
- **MongoDB URI**: `*******************************************/`
- **Database**: `bookmark_manager`
- **Collections**: `bookmarks`, `tags`

## Architecture

### Core Components

- **`src/main.py`**: Flask application entry point with CORS, routing, and error handling
- **`src/database.py`**: MongoDB connection management and initialization
- **`src/models/bookmark.py`**: Data model layer with bookmark CRUD operations
- **`src/routes/bookmark.py`**: REST API endpoints for bookmarks, tags, and statistics

### Key Patterns

- **Blueprint-based routing**: API routes organized under `/api` prefix
- **Singleton database**: Global `db_instance` and `bookmark_model` instances
- **Validation layer**: Input validation in model methods
- **Tag management**: Automatic tag statistics maintenance via `_update_tag_stats`

## API Endpoints

### Bookmarks
- `GET /api/bookmarks` - List bookmarks with filtering, pagination, sorting
- `POST /api/bookmarks` - Create new bookmark
- `GET /api/bookmarks/{id}` - Get bookmark by ID
- `PUT /api/bookmarks/{id}` - Update bookmark
- `DELETE /api/bookmarks/{id}` - Delete bookmark
- `DELETE /api/bookmarks/batch` - Batch delete bookmarks
- `POST /api/bookmarks/batch/tags` - Batch add tags to bookmarks

### Tags & Statistics
- `GET /api/tags` - Get tag statistics
- `GET /api/stats` - Get overall statistics

### Data Management
- `GET /api/sync/export` - Export all bookmarks
- `POST /api/sync/import` - Import bookmarks data

### Health Check
- `GET /api/health` - Service health status

## Data Models

### Bookmark Schema
```json
{
  "title": "string",
  "url": "string", 
  "tags": ["string"],
  "urgency": "high|medium|low",
  "importance": 1-5,
  "reminder": "ISO date",
  "comment": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## Error Handling

All endpoints return JSON responses with consistent format:
```json
{
  "success": boolean,
  "data": {...}, // or "error": {...}
}
```

## Common Tasks

### Adding New Features
1. Add route handler in `src/routes/bookmark.py`
2. Add corresponding model method in `src/models/bookmark.py`
3. Update validation and formatting as needed

### Database Operations
- Use `db_instance.get_collection('collection_name')` for direct MongoDB access
- Index creation handled automatically on startup in `database.py`
- Tag statistics maintained automatically via model methods

### Testing API Endpoints
```bash
# Health check
curl http://localhost:5000/api/health

# Get bookmarks with filtering
curl "http://localhost:5000/api/bookmarks?tags=frontend&limit=10"

# Create bookmark
curl -X POST http://localhost:5000/api/bookmarks \
  -H "Content-Type: application/json" \
  -d '{"title":"Test","url":"https://example.com","tags":["test"]}'
```